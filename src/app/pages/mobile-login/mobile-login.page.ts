import { Component, NgZone, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthenticateActivateResult, AuthenticateAndActivateResultType, AuthenticateLocalResult, AuthenticateLocalResultType, LoginListenerType, LoginParameters, LoginType, SettingsResult, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController, LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { USER_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { AppConstants } from 'src/app/shared/app-constants';

@Component({
  selector: 'app-mobile-login',
  templateUrl: './mobile-login.page.html',
  styleUrls: ['./mobile-login.page.scss'],
})
export class MobileLoginPage implements OnInit {
  public constants: AppConstants;
  public versionHide: boolean = true;
  public appVersion: string = '';
  public emailId: string = '';
  public password: string = '';
  public loginParameters: LoginParameters = null;
  public loginResultType: LoginListenerType = null;
  public company: string = '';
  public errorMessage: string = '';
  public showErrorMessage: boolean = false;
  public messageColor: string;
  public progressbar: boolean = false;
  public selectedUrl: string;
  public busyIndicator: any = null;
  public fieldTextType: boolean = false;
  
  constructor(
    private dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private route: Router,
    public loadingController: LoadingController,
    public translate: TranslateService,
    private ngZone: NgZone,
    public alertController: AlertController) { }

  ngOnInit() {
    window.addEventListener('keyboardDidHide', () => {
      this.versionHide = true;
    });
    window.addEventListener('keyboardWillShow', () => {
      this.versionHide = false;
    });
    this.constants = new AppConstants();
    this.loginResultType = this.dataService.loginResultType;
    let url = location.href;
    this.selectedUrl = this.dataService.getUmpUrl(url);
    console.log('URL :  ' + this.selectedUrl);
    this.appVersion = this.dataService.getAppVersion();
  }

  async login() {
    this.clearErrorMessage(); // Clear any previous error messages

    // Busy indicator, when other processes are running
    this.busyIndicator = await this.loadingController.create({
      message: this.translate.instant('Signing in...'),
      spinner: this.translate.instant('crescent'),
      animated: true,
      showBackdrop: true,
      translucent: true,
    });

    if (
      this.selectedUrl != null &&
      this.emailId != null &&
      this.password != null
    ) {
      // Set the arguments for ump
      this.loginParameters = new LoginParameters();
      this.loginParameters.url = this.selectedUrl;
      this.loginParameters.company = this.constants.LOGIN_PARAMS.COMPANY;
      this.loginParameters.loginType = LoginType.email;
      this.loginParameters.username = this.emailId.trim();
      this.loginParameters.password = this.password;
      this.loginParameters.jwtOptions = {
        app: 'PERMIT',
        language: 'en',
      };
      this.loginParameters.cacheWebData = true;

      // send selected language.
      this.loginParameters.loginLanguage = 'en';
      this.unviredSDK.logInfo(
        'Login',
        'login()',
        'Selected Language Code: ' + 'en'
      );
      this.loginResultType = this.loginResultType ?? 0;
      try {
        switch (this.loginResultType) {
          // |Authenticate and Activate|
          case LoginListenerType.auth_activation_required:
            await this.busyIndicator.present();
            let authenticateActivateResult: AuthenticateActivateResult;
            try {
              authenticateActivateResult =
                await this.unviredSDK.authenticateAndActivate(
                  this.loginParameters
                );
              // |Authentication activated and Login success|
              if (
                authenticateActivateResult.type ===
                AuthenticateAndActivateResultType.auth_activation_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateActivateResult.type ===
                AuthenticateAndActivateResultType.auth_activation_error
              ) {
                this.busyIndicator.dismiss();
                this.showErrorMessage = true;
                this.errorMessage = authenticateActivateResult.error || this.translate.instant('Login failed. Please check your credentials and try again.');
                this.messageColor = 'danger';
              }
            } catch (error) {
              this.busyIndicator.dismiss();
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant('Login failed. Please check your credentials and try again.');
              this.messageColor = 'danger';
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateAndActivate()',
                'ERROR: ' + error
              );
            }
            break;

          // |Authenticate Locally|
          case LoginListenerType.app_requires_login:
            this.busyIndicator.present();
            let authenticateLocalResult: AuthenticateLocalResult;
            try {
              authenticateLocalResult = await this.unviredSDK.authenticateLocal(
                this.loginParameters
              );
              // |Authenticate (Local) credentials saved in database|
              if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_success
              ) {
                // this.setLocalDataToStorage();
                this.busyIndicator.dismiss();
                this.displayLandingPage();
                this.emailId = '';
                this.password = '';
              } else if (
                authenticateLocalResult.type ===
                AuthenticateLocalResultType.login_error
              ) {
                this.busyIndicator.dismiss();
                this.showErrorMessage = true;
                this.errorMessage = authenticateLocalResult.error || this.translate.instant('Login failed. Please check your credentials and try again.');
                this.messageColor = 'danger';
              }
            } catch (error) {
              this.busyIndicator.dismiss();
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant('Login failed. Please check your credentials and try again.');
              this.messageColor = 'danger';
              this.unviredSDK.logError(
                'LoginPage',
                'authenticateLocal()',
                'ERROR: ' + error
              );
            }
            break;
        }
      } catch (error) {
        this.busyIndicator.dismiss();
        this.showErrorMessage = true;
        this.errorMessage = this.translate.instant('Login failed. Please check your credentials and try again.');
        this.messageColor = 'danger';
        this.unviredSDK.logError('LoginPage', 'login()', 'ERROR: ' + error);
      }
    }
  }

  // Navigate to landing page
  displayLandingPage() {
    // Flag for do customization only when user navigates from login page.
    this.loadingController?.dismiss();
    this.dataService.isStartCustomization = true;
    this.route.navigate(['mobile-home']);
  }

  // Alert, when get any error response from ump
  async presentAlert(errorResponse: string) {
    let showAlert = await this.alertController.create({
      header: this.translate.instant('Error'),
      message: errorResponse,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
        },
      ],
    });
    await showAlert.present();
  }

  forgetPassword(event) {
    event.preventDefault();

    //validate Email Id
    if (!this.emailId) {
      // Email Id is not present
      //display message asking for Email Id
      this.showErrorMessage = true;
      this.progressbar = false;
      this.errorMessage = this.translate.instant(
        'Email Id is required to reset password'
      );
      this.messageColor = 'danger';
      return;
    }

    //Email Id valid, disbale previous error messages if any
    console.log('Email Id is valid make api call');
    this.showErrorMessage = false;
    this.errorMessage = '';
    this.progressbar = true;
    //Email Id valid, make api call
    let EmailId = this.emailId;
    this.dataService
      .sendResetMail(EmailId)
      .toPromise()
      .then(
        async (response) => {
          this.messageColor = 'success';
          this.progressbar = false;
          if (response.status === 204) {
            this.ngZone.run(() => {
              this.showErrorMessage = true;
              this.errorMessage = this.translate.instant(
                'The reset password link has been sent to your email, please check your mailbox.'
              );
            });
          } else {
            this.showErrorMessage = true;
            this.errorMessage = this.translate.instant(
              'Email is not configured.You may wanna create account first.'
            );
          }
        },
        (error: any) => {
          console.log('API ERROR', error);
          this.showErrorMessage = true;
          this.progressbar = false;
          this.errorMessage = this.translate.instant(
            'Something went wrong please check company or email and try again.'
          );
          this.messageColor = 'danger';
        }
      );
  }

  onChangeDisableErrorMsg(event) {
    this.showErrorMessage = false;
    this.errorMessage = '';
  }

  getRedirectURL(url: any) {
    let redirectURL = url.substring(0, url.indexOf('/UMP'));
    return redirectURL;
  }

  async nustarLogin() {
    await this.presentLoading();
    let loginParameters = new LoginParameters();
    loginParameters.url = this.dataService.getUmpUrl();
    loginParameters.company = this.constants.LOGIN_PARAMS.COMPANY;

    loginParameters.jwtOptions = { app: 'PERMIT' };
    loginParameters.cacheWebData = true;

    // For SAML Login
    loginParameters.loginType = LoginType.saml2;
    loginParameters['redirectURL'] = 'https://umpdev.unvired.io/';

    let authenticateResult = await this.unviredSDK.authenticateAndActivate(
      loginParameters
    );
    if (
      authenticateResult.type ==
      AuthenticateAndActivateResultType.auth_activation_success
    ) {
      let settingsResult: SettingsResult = await this.unviredSDK.userSettings();
      if (settingsResult) {
        let userId: any = settingsResult.data.USER_ID;

        if (userId) {
          let customData = {
            USER: [
              {
                USER_HEADER: { USER_ID: userId },
              },
            ],
          };
          let res: any = await this.dataService.getUserById(customData);
          if (res.type == 0 && Object.keys(res.data).length > 0) {
            this.displayLandingPage();
          } else {
            // Caling create user
            let userObj = {} as USER_HEADER;
            userObj.P_MODE = 'A';
            userObj.ROLE_NAME = 'ADMIN';
            userObj.FIRST_NAME = settingsResult.data.FULL_NAME.split(' ')[0];
            userObj.LAST_NAME = settingsResult.data.FULL_NAME.split(' ')[1];
            userObj.EMAIL = settingsResult.data.EMAIL;
            userObj.USER_ID = settingsResult.data.USER_ID;
            userObj.PHONE = '';

            let customData = {
              AGENT: 'NUSTAR',
              USERS: [userObj],
            };
            let result: any = await this.dataService.createAgentUser(
              customData
            );
            if (result && result.type == 0) {
              this.displayLandingPage();
            }
          }
        }
      } else {
        this.loadingController?.dismiss();
      }
    } else {
      this.loadingController?.dismiss();
    }
    console.log('Auth Result: ' + JSON.stringify(authenticateResult, null, 2));
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Please wait...',
    });
    await loading.present();
  }

  clearErrorMessage() {
    this.showErrorMessage = false;
    this.errorMessage = '';
  }
}
